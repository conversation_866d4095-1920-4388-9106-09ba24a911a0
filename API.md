# Push-Swap API Documentation

This document describes the public API of the `push-swap/pkg/pushswap` package.

## Package Import

```go
import "push-swap/pkg/pushswap"
```

## Core Data Structures

### Stack

The `Stack` struct represents a stack data structure with LIFO (Last In, First Out) behavior.

```go
type Stack struct {
    values []int
}
```

#### Methods

##### `NewStack() *Stack`
Creates and returns a new empty stack.

##### `Push(value int)`
Pushes a value onto the top of the stack.

##### `Pop() (int, bool)`
Removes and returns the top value from the stack. Returns `(0, false)` if stack is empty.

##### `Peek() (int, bool)`
Returns the top value without removing it. Returns `(0, false)` if stack is empty.

##### `Top() (int, bool)`
Alias for `Peek()`. Returns the top value without removing it.

##### `Second() (int, bool)`
Returns the second element from the top without removing it. Returns `(0, false)` if stack has fewer than 2 elements.

##### `IsEmpty() bool`
Returns `true` if the stack is empty, `false` otherwise.

##### `Size() int`
Returns the number of elements in the stack.

##### `IsSorted() bool`
Returns `true` if the stack is sorted in ascending order (smallest at top), `false` otherwise.

##### `Clone() *Stack`
Creates and returns a deep copy of the stack.

##### `GetValues() []int`
Returns a copy of the stack's values as a slice (bottom to top order).

## Stack Operations

All operations follow the push-swap specification and work on two stacks (a and b).

### Swap Operations

#### `sa(a, b *Stack)`
Swaps the first 2 elements at the top of stack a. Does nothing if there are fewer than 2 elements.

#### `sb(a, b *Stack)`
Swaps the first 2 elements at the top of stack b. Does nothing if there are fewer than 2 elements.

#### `ss(a, b *Stack)`
Executes `sa` and `sb` simultaneously.

### Push Operations

#### `pa(a, b *Stack)`
Pushes the top element of stack b to stack a. Does nothing if stack b is empty.

#### `pb(a, b *Stack)`
Pushes the top element of stack a to stack b. Does nothing if stack a is empty.

### Rotate Operations

#### `ra(a, b *Stack)`
Rotates stack a up. The first element becomes the last one.

#### `rb(a, b *Stack)`
Rotates stack b up. The first element becomes the last one.

#### `rr(a, b *Stack)`
Executes `ra` and `rb` simultaneously.

### Reverse Rotate Operations

#### `rra(a, b *Stack)`
Rotates stack a down. The last element becomes the first one.

#### `rrb(a, b *Stack)`
Rotates stack b down. The last element becomes the first one.

#### `rrr(a, b *Stack)`
Executes `rra` and `rrb` simultaneously.

### Operation Execution

#### `ExecuteOperation(op string, a, b *Stack) error`
Executes a single operation by name. Returns an error if the operation is invalid.

**Valid operations**: "sa", "sb", "ss", "pa", "pb", "ra", "rb", "rr", "rra", "rrb", "rrr"

## Parsing Functions

### `ParseArguments(args []string) ([]int, error)`
Parses command-line arguments into a slice of integers.

**Input formats supported**:
- Multiple arguments: `["1", "2", "3"]`
- Single quoted string: `["1 2 3"]`
- Mixed: `["1", "2 3", "4"]`

**Returns**:
- `[]int`: Parsed integers
- `error`: Validation error (duplicates, non-integers, etc.)

## Algorithm Functions

### Helper Functions

#### `findSmallestIndex(stack *Stack) int`
Returns the index of the smallest element in the stack (from bottom). Returns -1 if stack is empty.

#### `findLargestIndex(stack *Stack) int`
Returns the index of the largest element in the stack (from bottom). Returns -1 if stack is empty.

#### `getOptimalPosition(val int, stack *Stack) int`
Calculates the optimal position to insert a value in a sorted stack to maintain order.

#### `calculateCost(indexA, indexB, sizeA, sizeB int) int`
Calculates the cost of moving elements at given indices in both stacks.

### Sorting Algorithms

#### `sortThree(a *Stack, instructions *[]string)`
Optimally sorts exactly 3 elements using at most 2 operations.

#### `sortSmall(a, b *Stack, instructions *[]string)`
Sorts arrays of 1-5 elements using optimal algorithms.

#### `sortMedium(a, b *Stack, instructions *[]string)`
Sorts medium-sized arrays (6-20 elements) using specialized algorithms.

#### `radixSort(a, b *Stack, instructions *[]string)`
Sorts large arrays using optimized radix sort algorithm.

#### `PushSwapAlgorithm(numbers []int) []string`
Main algorithm entry point. Returns the sequence of operations to sort the input.

## Utility Functions

### `IsAlreadySorted(numbers []int) bool`
Checks if an array is already sorted in ascending order.

### `CreateStackFromNumbers(numbers []int) *Stack`
Creates a stack from a slice of integers (first element becomes bottom of stack).

## Error Handling

### Common Errors

- **Invalid integer**: Non-numeric input
- **Duplicate numbers**: Same number appears multiple times
- **Invalid operation**: Unknown operation name
- **Empty input**: No arguments provided (not an error, returns empty result)

### Error Format

All errors are returned as Go `error` types with descriptive messages. Programs output "Error\n" to stderr and exit with status code 1.

## Performance Characteristics

### Time Complexity

- **Small arrays (≤5)**: O(1) - constant time with optimal solutions
- **Medium arrays (6-20)**: O(n²) - quadratic due to element positioning
- **Large arrays (20+)**: O(n log n) - radix sort with bit operations

### Space Complexity

- **All algorithms**: O(n) - uses two stacks plus instruction storage
- **Memory usage**: Linear with input size

### Instruction Counts

| Input Size | Expected Operations | Algorithm Used |
|------------|-------------------|----------------|
| 2-3 elements | ≤ 3 | Hardcoded optimal |
| 4-5 elements | ≤ 12 | Small array algorithm |
| 6 elements | ≤ 9 | Specialized medium algorithm |
| 100 elements | ≤ 700 | Optimized radix sort |
| 500 elements | ≤ 5500 | Optimized radix sort |
