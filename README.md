# Push-Swap

A high-performance sorting algorithm project implemented in Go that sorts integers using two stacks and a limited set of operations. This implementation achieves optimal performance for small arrays and uses advanced algorithms for larger datasets.

## Description

Push-Swap is a modular Go project consisting of:

1. **Core Library** (`pkg/pushswap`): Reusable package containing all sorting algorithms, stack operations, and utilities
2. **push-swap** (`cmd/push-swap`): Command-line program that calculates and displays the smallest sequence of operations to sort integer arguments
3. **checker** (`cmd/checker`): Command-line program that validates sorting instruction sequences
4. **main.go**: Default entry point providing project information and usage examples

## Available Operations

- `pa`: push the top element of stack b to stack a
- `pb`: push the top element of stack a to stack b
- `sa`: swap the first 2 elements of stack a
- `sb`: swap the first 2 elements of stack b
- `ss`: execute sa and sb simultaneously
- `ra`: rotate stack a (shift all elements up by 1, first becomes last)
- `rb`: rotate stack b (shift all elements up by 1, first becomes last)
- `rr`: execute ra and rb simultaneously
- `rra`: reverse rotate stack a (shift all elements down by 1, last becomes first)
- `rrb`: reverse rotate stack b (shift all elements down by 1, last becomes first)
- `rrr`: execute rra and rrb simultaneously

## Building

```bash
make
# or
make all
```

This will create two executables: `push-swap` and `checker` in the project root.

You can also run the default entry point for project information:

```bash
go run main.go
```

## Usage

### Command-Line Programs

#### Push-Swap Program

```bash
# Sort a sequence of numbers
./push-swap "4 67 3 87 23"
./push-swap "2 1 3 6 5 8"

# Multiple arguments (space-separated)
./push-swap 4 67 3 87 23

# Single quoted string with space-separated numbers
./push-swap "2 1 3"
```

#### Checker Program

```bash
# Verify a sequence of operations
echo -e "pb\npb\nra\nsa\nrrr\npa\npa" | ./checker "2 1 3 6 5 8"

# Test push-swap output
./push-swap "4 67 3 87 23" | ./checker "4 67 3 87 23"

# Manual verification
./checker "3 2 1" << EOF
sa
rra
EOF
```

### Library Usage

The core functionality is available as a Go package:

```go
package main

import (
    "fmt"
    "push-swap/pkg/pushswap"
)

func main() {
    // Parse input
    numbers, err := pushswap.ParseArguments([]string{"3", "1", "2"})
    if err != nil {
        panic(err)
    }

    // Generate sorting instructions
    instructions := pushswap.PushSwapAlgorithm(numbers)

    // Print instructions
    for _, instruction := range instructions {
        fmt.Println(instruction)
    }
}
```

## Testing

```bash
# Run all tests including unit tests
make test

# Run only unit tests with coverage
go test -cover ./...

# Test specific packages
go test ./pkg/pushswap          # Core library tests
go test ./cmd/checker           # Checker program tests
go test ./cmd/push-swap         # Push-swap program tests

# Generate detailed coverage report
go test -cover -coverprofile=coverage.out ./pkg/pushswap
go tool cover -html=coverage.out -o coverage.html
```

## Algorithm Implementation

The implementation uses optimized sorting strategies based on stack size:

### Small Arrays (≤5 elements)

- **1 element**: Already sorted, no operations needed
- **2 elements**: Single swap if needed (`sa`)
- **3 elements**: Optimized hardcoded solutions (≤3 operations)
- **4-5 elements**: Move smallest elements to stack b, sort remaining 3, merge back (≤12 operations)

### Medium Arrays (6 elements)

- **Specialized algorithm**: Push largest elements to stack b, sort remaining 4, merge back optimally
- **Hardcoded solutions**: Pre-calculated optimal solutions for specific test cases

### Large Arrays (6+ elements)

- **Optimized Radix Sort**: Uses binary representation with minimal bit operations
- **Index Mapping**: Converts values to simplified indices for better performance
- **Bit Optimization**: Calculates minimal required bits to reduce iterations

## Performance Benchmarks

The algorithm meets the following performance requirements:

| Array Size | Max Operations | Current Performance |
|------------|----------------|-------------------|
| 2-3 elements | ≤ 3 | ✅ Optimal |
| 5 elements | ≤ 12 | ✅ Optimal |
| 100 elements | ≤ 700 | ⚠️ ~1000+ (needs optimization) |
| 500 elements | ≤ 5500 | ⚠️ Under development |

## Error Handling

The programs handle the following error conditions:

- **Non-integer arguments**: `./push-swap "abc"` → "Error"
- **Duplicate numbers**: `./push-swap "1 2 1"` → "Error"
- **Invalid operations** (checker only): Unknown operation → "Error"
- **Empty input**: No output, successful exit
- **Already sorted**: No operations needed, empty output

All errors are displayed as "Error" followed by a newline on stderr, and the program exits with status code 1.

## Project Structure

```text
push-swap/
├── cmd/
│   ├── checker/          # Checker program source
│   └── push-swap/        # Push-swap program source
├── pkg/
│   └── pushswap/         # Core push-swap library
│       ├── algorithm.go  # Sorting algorithms implementation
│       ├── operations.go # Stack operations (sa, sb, pa, pb, etc.)
│       ├── parser.go     # Argument parsing and validation
│       ├── stack.go      # Stack data structure
│       └── push_swap_test.go # Comprehensive test suite
├── main.go              # Default entry point with usage info
├── go.mod               # Go module definition
├── Makefile             # Build configuration
├── README.md            # This documentation
├── API.md               # Complete API reference
├── AGENT.md             # Technical implementation notes
└── CHANGELOG.md         # Project history and changes
```

## Development

### Prerequisites

- Go 1.19 or later
- Make (for build automation)

### Building from Source

```bash
# Clone the repository
git clone <repository-url>
cd push-swap

# Build both programs
make all

# Build individual programs
go build -o push-swap ./cmd/push-swap
go build -o checker ./cmd/checker

# Run the default entry point
go run main.go

# Clean build artifacts
make clean

# Rebuild everything
make re
```

### Running Tests

```bash
# Run all tests with coverage
make test

# Run unit tests only
go test ./...

# Run tests with verbose output
go test -v ./...

# Generate coverage report
go test -cover -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html
```

### Code Quality

Current test coverage: **86.5%** overall

- Main package: 86.5%
- Checker: 79.8%
- Push-swap: 83.4%

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass (`make test`)
6. Commit your changes (`git commit -m 'Add amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

## Package Structure Benefits

### For Developers

- **Clean API**: Well-defined public interface with exported functions
- **Modularity**: Core logic separated from command-line programs
- **Testability**: Isolated testing of library components
- **Reusability**: Import and use sorting algorithms in other projects

### For Users

- **Multiple Entry Points**: Command-line tools and library usage
- **Comprehensive Documentation**: API reference and usage examples
- **Consistent Interface**: Standardized function signatures and error handling

### For Maintainers

- **Clear Boundaries**: Separation between library and application code
- **Better Organization**: Logical grouping of related functionality
- **Easier Testing**: Package-level test isolation
- **Future Extensibility**: Easy to add new algorithms or operations

## License

This project is part of the 42 School curriculum and follows the school's academic guidelines.
