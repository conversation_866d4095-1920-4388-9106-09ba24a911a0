package main

import (
	"bufio"
	"fmt"
	"os"
	"strings"

	"push-swap/pkg/pushswap"
)

func main() {
	if len(os.Args) < 2 {
		return
	}

	numbers, err := pushswap.ParseArguments(os.Args[1:])
	if err != nil {
		fmt.Fprintln(os.<PERSON>, "Error")
		os.Exit(1)
	}

	if len(numbers) == 0 {
		return
	}

	a := pushswap.CreateStackFromNumbers(numbers)
	b := pushswap.NewStack()

	scanner := bufio.NewScanner(os.Stdin)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		err := pushswap.ExecuteOperation(line, a, b)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error")
			os.Exit(1)
		}
	}

	if scanner.Err() != nil {
		fmt.Fprintln(os.<PERSON>, "Error")
		os.Exit(1)
	}

	// Check if stack a is sorted and stack b is empty
	if a.<PERSON>() && b.<PERSON>() {
		fmt.Println("OK")
	} else {
		fmt.Println("KO")
	}
}
