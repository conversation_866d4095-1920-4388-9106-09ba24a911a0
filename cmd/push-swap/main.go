package main

import (
	"fmt"
	"os"

	"push-swap/pkg/pushswap"
)

func main() {
	if len(os.Args) < 2 {
		return
	}

	numbers, err := pushswap.ParseArguments(os.Args[1:])
	if err != nil {
		fmt.Fprintln(os.<PERSON>, "Error")
		os.Exit(1)
	}

	if len(numbers) == 0 {
		return
	}

	instructions := pushswap.PushSwapAlgorithm(numbers)
	for _, instruction := range instructions {
		fmt.Println(instruction)
	}
}
