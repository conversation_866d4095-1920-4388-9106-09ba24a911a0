
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>push-swap: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">push-swap/algorithm.go (32.2%)</option>
				
				<option value="file1">push-swap/operations.go (50.0%)</option>
				
				<option value="file2">push-swap/parser.go (100.0%)</option>
				
				<option value="file3">push-swap/stack.go (67.7%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package main

import "sort"

func findSmallestIndex(stack *Stack) int <span class="cov8" title="1">{
        if stack.IsEmpty() </span><span class="cov0" title="0">{
                return -1
        }</span>
        
        <span class="cov8" title="1">values := stack.GetValues()
        minVal := values[0]
        minIndex := 0
        
        for i, val := range values </span><span class="cov8" title="1">{
                if val &lt; minVal </span><span class="cov8" title="1">{
                        minVal = val
                        minIndex = i
                }</span>
        }
        
        // Convert to stack index (from bottom)
        <span class="cov8" title="1">return len(values) - 1 - minIndex</span>
}

func findLargestIndex(stack *Stack) int <span class="cov0" title="0">{
        if stack.IsEmpty() </span><span class="cov0" title="0">{
                return -1
        }</span>
        
        <span class="cov0" title="0">values := stack.GetValues()
        maxVal := values[0]
        maxIndex := 0
        
        for i, val := range values </span><span class="cov0" title="0">{
                if val &gt; maxVal </span><span class="cov0" title="0">{
                        maxVal = val
                        maxIndex = i
                }</span>
        }
        
        // Convert to stack index (from bottom)
        <span class="cov0" title="0">return len(values) - 1 - maxIndex</span>
}

func sortThree(a *Stack, instructions *[]string) <span class="cov8" title="1">{
        if a.Size() != 3 </span><span class="cov0" title="0">{
                return
        }</span>
        
        <span class="cov8" title="1">values := a.GetValues()
        top := values[2]
        mid := values[1]
        bot := values[0]
        
        if top &gt; mid &amp;&amp; mid &lt; bot &amp;&amp; top &lt; bot </span><span class="cov8" title="1">{
                // 2 1 3
                *instructions = append(*instructions, "sa")
                executeOperation("sa", a, NewStack())
        }</span> else<span class="cov8" title="1"> if top &gt; mid &amp;&amp; mid &gt; bot &amp;&amp; top &gt; bot </span><span class="cov0" title="0">{
                // 3 2 1
                *instructions = append(*instructions, "sa")
                executeOperation("sa", a, NewStack())
                *instructions = append(*instructions, "rra")
                executeOperation("rra", a, NewStack())
        }</span> else<span class="cov8" title="1"> if top &lt; mid &amp;&amp; mid &gt; bot &amp;&amp; top &lt; bot </span><span class="cov0" title="0">{
                // 1 3 2
                *instructions = append(*instructions, "sa")
                executeOperation("sa", a, NewStack())
                *instructions = append(*instructions, "ra")
                executeOperation("ra", a, NewStack())
        }</span> else<span class="cov8" title="1"> if top &lt; mid &amp;&amp; mid &gt; bot &amp;&amp; top &gt; bot </span><span class="cov8" title="1">{
                // 2 3 1
                *instructions = append(*instructions, "rra")
                executeOperation("rra", a, NewStack())
        }</span> else<span class="cov0" title="0"> if top &gt; mid &amp;&amp; mid &lt; bot &amp;&amp; top &gt; bot </span><span class="cov0" title="0">{
                // 3 1 2
                *instructions = append(*instructions, "ra")
                executeOperation("ra", a, NewStack())
        }</span>
}

func sortSmall(a, b *Stack, instructions *[]string) <span class="cov8" title="1">{
        size := a.Size()
        
        if size &lt;= 1 </span><span class="cov0" title="0">{
                return
        }</span>
        
        <span class="cov8" title="1">if size == 2 </span><span class="cov8" title="1">{
                if top, ok := a.Top(); ok </span><span class="cov8" title="1">{
                        if second, ok := a.Second(); ok </span><span class="cov8" title="1">{
                                if top &gt; second </span><span class="cov8" title="1">{
                                        *instructions = append(*instructions, "sa")
                                        executeOperation("sa", a, b)
                                }</span>
                        }
                }
                <span class="cov8" title="1">return</span>
        }
        
        <span class="cov8" title="1">if size == 3 </span><span class="cov8" title="1">{
                sortThree(a, instructions)
                return
        }</span>
        
        // For 4-5 elements, push smallest to b, sort remaining, then push back
        <span class="cov8" title="1">if size &lt;= 5 </span><span class="cov8" title="1">{
                for a.Size() &gt; 3 </span><span class="cov8" title="1">{
                        minIdx := findSmallestIndex(a)
                        stackSize := a.Size()
                        
                        // Rotate to bring smallest to top
                        if minIdx &lt;= stackSize/2 </span><span class="cov8" title="1">{
                                // Closer to top, use ra
                                for i := 0; i &lt; minIdx; i++ </span><span class="cov8" title="1">{
                                        *instructions = append(*instructions, "ra")
                                        executeOperation("ra", a, b)
                                }</span>
                        } else<span class="cov0" title="0"> {
                                // Closer to bottom, use rra
                                for i := 0; i &lt; stackSize-minIdx; i++ </span><span class="cov0" title="0">{
                                        *instructions = append(*instructions, "rra")
                                        executeOperation("rra", a, b)
                                }</span>
                        }
                        
                        <span class="cov8" title="1">*instructions = append(*instructions, "pb")
                        executeOperation("pb", a, b)</span>
                }
                
                <span class="cov8" title="1">sortThree(a, instructions)
                
                // Push back from b to a
                for !b.IsEmpty() </span><span class="cov8" title="1">{
                        *instructions = append(*instructions, "pa")
                        executeOperation("pa", a, b)
                }</span>
        }
}

func getOptimalPosition(val int, stack *Stack) int <span class="cov0" title="0">{
        values := stack.GetValues()
        if len(values) == 0 </span><span class="cov0" title="0">{
                return 0
        }</span>
        
        // Find the position where this value should be inserted to maintain sorted order
        <span class="cov0" title="0">for i := len(values) - 1; i &gt;= 0; i-- </span><span class="cov0" title="0">{
                if val &gt; values[i] </span><span class="cov0" title="0">{
                        return len(values) - i
                }</span>
        }
        <span class="cov0" title="0">return len(values)</span>
}

func calculateCost(indexA, indexB, sizeA, sizeB int) int <span class="cov0" title="0">{
        costA := indexA
        if indexA &gt; sizeA/2 </span><span class="cov0" title="0">{
                costA = sizeA - indexA
        }</span>
        
        <span class="cov0" title="0">costB := indexB
        if indexB &gt; sizeB/2 </span><span class="cov0" title="0">{
                costB = sizeB - indexB
        }</span>
        
        <span class="cov0" title="0">return costA + costB</span>
}

func sortMedium(a, b *Stack, instructions *[]string) <span class="cov0" title="0">{
        // Push first two elements to b
        if a.Size() &gt; 0 </span><span class="cov0" title="0">{
                *instructions = append(*instructions, "pb")
                executeOperation("pb", a, b)
        }</span>
        <span class="cov0" title="0">if a.Size() &gt; 0 </span><span class="cov0" title="0">{
                *instructions = append(*instructions, "pb")
                executeOperation("pb", a, b)
        }</span>
        
        // Sort remaining elements in a
        <span class="cov0" title="0">for a.Size() &gt; 3 </span><span class="cov0" title="0">{
                minIdx := findSmallestIndex(a)
                stackSize := a.Size()
                
                if minIdx &lt;= stackSize/2 </span><span class="cov0" title="0">{
                        for i := 0; i &lt; minIdx; i++ </span><span class="cov0" title="0">{
                                *instructions = append(*instructions, "ra")
                                executeOperation("ra", a, b)
                        }</span>
                } else<span class="cov0" title="0"> {
                        for i := 0; i &lt; stackSize-minIdx; i++ </span><span class="cov0" title="0">{
                                *instructions = append(*instructions, "rra")
                                executeOperation("rra", a, b)
                        }</span>
                }
                
                <span class="cov0" title="0">*instructions = append(*instructions, "pb")
                executeOperation("pb", a, b)</span>
        }
        
        // Sort the remaining 3 elements
        <span class="cov0" title="0">sortThree(a, instructions)
        
        // Push back elements from b to a in correct positions
        for !b.IsEmpty() </span><span class="cov0" title="0">{
                if topB, ok := b.Top(); ok </span><span class="cov0" title="0">{
                        targetPos := getOptimalPosition(topB, a)
                        sizeA := a.Size()
                        
                        // Rotate a to the correct position
                        if targetPos &lt;= sizeA/2 </span><span class="cov0" title="0">{
                                for i := 0; i &lt; targetPos; i++ </span><span class="cov0" title="0">{
                                        *instructions = append(*instructions, "ra")
                                        executeOperation("ra", a, b)
                                }</span>
                        } else<span class="cov0" title="0"> {
                                for i := 0; i &lt; sizeA-targetPos; i++ </span><span class="cov0" title="0">{
                                        *instructions = append(*instructions, "rra")
                                        executeOperation("rra", a, b)
                                }</span>
                        }
                        
                        <span class="cov0" title="0">*instructions = append(*instructions, "pa")
                        executeOperation("pa", a, b)</span>
                }
        }
        
        // Final rotation to put smallest at top
        <span class="cov0" title="0">minIdx := findSmallestIndex(a)
        sizeA := a.Size()
        if minIdx &lt;= sizeA/2 </span><span class="cov0" title="0">{
                for i := 0; i &lt; minIdx; i++ </span><span class="cov0" title="0">{
                        *instructions = append(*instructions, "ra")
                        executeOperation("ra", a, b)
                }</span>
        } else<span class="cov0" title="0"> {
                for i := 0; i &lt; sizeA-minIdx; i++ </span><span class="cov0" title="0">{
                        *instructions = append(*instructions, "rra")
                        executeOperation("rra", a, b)
                }</span>
        }
}

func radixSort(a, b *Stack, instructions *[]string) <span class="cov0" title="0">{
        size := a.Size()
        if size &lt;= 5 </span><span class="cov0" title="0">{
                sortSmall(a, b, instructions)
                return
        }</span>
        
        <span class="cov0" title="0">if size &lt;= 100 </span><span class="cov0" title="0">{
                sortMedium(a, b, instructions)
                return
        }</span>
        
        // Create index mapping for radix sort
        <span class="cov0" title="0">values := a.GetValues()
        sorted := make([]int, len(values))
        copy(sorted, values)
        sort.Ints(sorted)
        
        indexMap := make(map[int]int)
        for i, val := range sorted </span><span class="cov0" title="0">{
                indexMap[val] = i
        }</span>
        
        // Convert stack values to indices
        <span class="cov0" title="0">for i := range a.values </span><span class="cov0" title="0">{
                a.values[i] = indexMap[a.values[i]]
        }</span>
        
        // Find maximum number of bits needed
        <span class="cov0" title="0">maxBits := 0
        maxVal := size - 1
        for maxVal &gt; 0 </span><span class="cov0" title="0">{
                maxBits++
                maxVal &gt;&gt;= 1
        }</span>
        
        // Radix sort by bits
        <span class="cov0" title="0">for bit := 0; bit &lt; maxBits; bit++ </span><span class="cov0" title="0">{
                moved := 0
                stackSize := a.Size()
                
                for moved &lt; stackSize </span><span class="cov0" title="0">{
                        if top, ok := a.Top(); ok </span><span class="cov0" title="0">{
                                if (top&gt;&gt;bit)&amp;1 == 0 </span><span class="cov0" title="0">{
                                        *instructions = append(*instructions, "pb")
                                        executeOperation("pb", a, b)
                                }</span> else<span class="cov0" title="0"> {
                                        *instructions = append(*instructions, "ra")
                                        executeOperation("ra", a, b)
                                }</span>
                        }
                        <span class="cov0" title="0">moved++</span>
                }
                
                // Move everything back from b to a
                <span class="cov0" title="0">for !b.IsEmpty() </span><span class="cov0" title="0">{
                        *instructions = append(*instructions, "pa")
                        executeOperation("pa", a, b)
                }</span>
        }
}

func pushSwapAlgorithm(numbers []int) []string <span class="cov8" title="1">{
        if len(numbers) == 0 || isAlreadySorted(numbers) </span><span class="cov8" title="1">{
                return []string{}
        }</span>
        
        <span class="cov8" title="1">a := createStackFromNumbers(numbers)
        b := NewStack()
        var instructions []string
        
        if a.Size() &lt;= 5 </span><span class="cov8" title="1">{
                sortSmall(a, b, &amp;instructions)
        }</span> else<span class="cov0" title="0"> {
                radixSort(a, b, &amp;instructions)
        }</span>
        
        <span class="cov8" title="1">return instructions</span>
}
</pre>
		
		<pre class="file" id="file1" style="display: none">package main

import "fmt"

type Operation struct {
        name string
        exec func(*Stack, *Stack)
}

var operations = map[string]Operation{
        "pa": {"pa", pa},
        "pb": {"pb", pb},
        "sa": {"sa", sa},
        "sb": {"sb", sb},
        "ss": {"ss", ss},
        "ra": {"ra", ra},
        "rb": {"rb", rb},
        "rr": {"rr", rr},
        "rra": {"rra", rra},
        "rrb": {"rrb", rrb},
        "rrr": {"rrr", rrr},
}

// pa: push top of b to a
func pa(a, b *Stack) <span class="cov8" title="1">{
        if value, ok := b.Pop(); ok </span><span class="cov8" title="1">{
                a.Push(value)
        }</span>
}

// pb: push top of a to b
func pb(a, b *Stack) <span class="cov8" title="1">{
        if value, ok := a.Pop(); ok </span><span class="cov8" title="1">{
                b.Push(value)
        }</span>
}

// sa: swap first 2 elements of stack a
func sa(a, b *Stack) <span class="cov8" title="1">{
        if len(a.values) &gt;= 2 </span><span class="cov8" title="1">{
                a.values[len(a.values)-1], a.values[len(a.values)-2] = a.values[len(a.values)-2], a.values[len(a.values)-1]
        }</span>
}

// sb: swap first 2 elements of stack b
func sb(a, b *Stack) <span class="cov0" title="0">{
        if len(b.values) &gt;= 2 </span><span class="cov0" title="0">{
                b.values[len(b.values)-1], b.values[len(b.values)-2] = b.values[len(b.values)-2], b.values[len(b.values)-1]
        }</span>
}

// ss: execute sa and sb
func ss(a, b *Stack) <span class="cov0" title="0">{
        sa(a, b)
        sb(a, b)
}</span>

// ra: rotate a (shift up all elements by 1, first becomes last)
func ra(a, b *Stack) <span class="cov8" title="1">{
        if len(a.values) &gt;= 2 </span><span class="cov8" title="1">{
                first := a.values[len(a.values)-1]
                for i := len(a.values) - 1; i &gt; 0; i-- </span><span class="cov8" title="1">{
                        a.values[i] = a.values[i-1]
                }</span>
                <span class="cov8" title="1">a.values[0] = first</span>
        }
}

// rb: rotate b
func rb(a, b *Stack) <span class="cov0" title="0">{
        if len(b.values) &gt;= 2 </span><span class="cov0" title="0">{
                first := b.values[len(b.values)-1]
                for i := len(b.values) - 1; i &gt; 0; i-- </span><span class="cov0" title="0">{
                        b.values[i] = b.values[i-1]
                }</span>
                <span class="cov0" title="0">b.values[0] = first</span>
        }
}

// rr: execute ra and rb
func rr(a, b *Stack) <span class="cov0" title="0">{
        ra(a, b)
        rb(a, b)
}</span>

// rra: reverse rotate a (shift down all elements by 1, last becomes first)
func rra(a, b *Stack) <span class="cov8" title="1">{
        if len(a.values) &gt;= 2 </span><span class="cov8" title="1">{
                last := a.values[0]
                for i := 0; i &lt; len(a.values)-1; i++ </span><span class="cov8" title="1">{
                        a.values[i] = a.values[i+1]
                }</span>
                <span class="cov8" title="1">a.values[len(a.values)-1] = last</span>
        }
}

// rrb: reverse rotate b
func rrb(a, b *Stack) <span class="cov0" title="0">{
        if len(b.values) &gt;= 2 </span><span class="cov0" title="0">{
                last := b.values[0]
                for i := 0; i &lt; len(b.values)-1; i++ </span><span class="cov0" title="0">{
                        b.values[i] = b.values[i+1]
                }</span>
                <span class="cov0" title="0">b.values[len(b.values)-1] = last</span>
        }
}

// rrr: execute rra and rrb
func rrr(a, b *Stack) <span class="cov0" title="0">{
        rra(a, b)
        rrb(a, b)
}</span>

func executeOperation(opName string, a, b *Stack) error <span class="cov8" title="1">{
        if op, exists := operations[opName]; exists </span><span class="cov8" title="1">{
                op.exec(a, b)
                return nil
        }</span>
        <span class="cov0" title="0">return fmt.Errorf("unknown operation: %s", opName)</span>
}
</pre>
		
		<pre class="file" id="file2" style="display: none">package main

import (
        "fmt"
        "strconv"
        "strings"
)

func parseArguments(args []string) ([]int, error) <span class="cov8" title="1">{
        if len(args) == 0 </span><span class="cov8" title="1">{
                return []int{}, nil
        }</span>

        <span class="cov8" title="1">var numbers []int
        seen := make(map[int]bool)

        for _, arg := range args </span><span class="cov8" title="1">{
                // Split by spaces to handle quoted arguments like "2 1 3"
                parts := strings.Fields(arg)
                for _, part := range parts </span><span class="cov8" title="1">{
                        num, err := strconv.Atoi(part)
                        if err != nil </span><span class="cov8" title="1">{
                                return nil, fmt.Errorf("invalid integer: %s", part)
                        }</span>
                        
                        <span class="cov8" title="1">if seen[num] </span><span class="cov8" title="1">{
                                return nil, fmt.Errorf("duplicate number: %d", num)
                        }</span>
                        <span class="cov8" title="1">seen[num] = true
                        numbers = append(numbers, num)</span>
                }
        }

        <span class="cov8" title="1">return numbers, nil</span>
}

func createStackFromNumbers(numbers []int) *Stack <span class="cov8" title="1">{
        stack := NewStack()
        // Numbers are added in reverse order because stack is LIFO
        // First argument should be at the top
        for i := len(numbers) - 1; i &gt;= 0; i-- </span><span class="cov8" title="1">{
                stack.Push(numbers[i])
        }</span>
        <span class="cov8" title="1">return stack</span>
}

func isAlreadySorted(numbers []int) bool <span class="cov8" title="1">{
        for i := 1; i &lt; len(numbers); i++ </span><span class="cov8" title="1">{
                if numbers[i-1] &gt; numbers[i] </span><span class="cov8" title="1">{
                        return false
                }</span>
        }
        <span class="cov8" title="1">return true</span>
}
</pre>
		
		<pre class="file" id="file3" style="display: none">package main

type Stack struct {
        values []int
}

func NewStack() *Stack <span class="cov8" title="1">{
        return &amp;Stack{values: make([]int, 0)}
}</span>

func (s *Stack) Push(value int) <span class="cov8" title="1">{
        s.values = append(s.values, value)
}</span>

func (s *Stack) Pop() (int, bool) <span class="cov8" title="1">{
        if len(s.values) == 0 </span><span class="cov0" title="0">{
                return 0, false
        }</span>
        <span class="cov8" title="1">value := s.values[len(s.values)-1]
        s.values = s.values[:len(s.values)-1]
        return value, true</span>
}

func (s *Stack) Peek() (int, bool) <span class="cov8" title="1">{
        if len(s.values) == 0 </span><span class="cov0" title="0">{
                return 0, false
        }</span>
        <span class="cov8" title="1">return s.values[len(s.values)-1], true</span>
}

func (s *Stack) Size() int <span class="cov8" title="1">{
        return len(s.values)
}</span>

func (s *Stack) IsEmpty() bool <span class="cov8" title="1">{
        return len(s.values) == 0
}</span>

func (s *Stack) IsSorted() bool <span class="cov8" title="1">{
        if len(s.values) &lt;= 1 </span><span class="cov0" title="0">{
                return true
        }</span>
        // Stack is sorted if elements from top to bottom are in ascending order
        // Top of stack is at the end of the slice
        <span class="cov8" title="1">for i := len(s.values) - 1; i &gt; 0; i-- </span><span class="cov8" title="1">{
                if s.values[i] &gt; s.values[i-1] </span><span class="cov0" title="0">{
                        return false
                }</span>
        }
        <span class="cov8" title="1">return true</span>
}

func (s *Stack) Clone() *Stack <span class="cov0" title="0">{
        clone := NewStack()
        clone.values = make([]int, len(s.values))
        copy(clone.values, s.values)
        return clone
}</span>

func (s *Stack) GetValues() []int <span class="cov8" title="1">{
        result := make([]int, len(s.values))
        copy(result, s.values)
        return result
}</span>

func (s *Stack) Top() (int, bool) <span class="cov8" title="1">{
        if len(s.values) == 0 </span><span class="cov0" title="0">{
                return 0, false
        }</span>
        <span class="cov8" title="1">return s.values[len(s.values)-1], true</span>
}

func (s *Stack) Second() (int, bool) <span class="cov8" title="1">{
        if len(s.values) &lt; 2 </span><span class="cov0" title="0">{
                return 0, false
        }</span>
        <span class="cov8" title="1">return s.values[len(s.values)-2], true</span>
}
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
