package main

import (
	"fmt"
	"os"
)

func main() {
	fmt.Println("Push-Swap Project")
	fmt.Println("================")
	fmt.Println()
	fmt.Println("This project provides two main programs:")
	fmt.Println("  • push-swap: Generates optimal sorting instructions")
	fmt.Println("  • checker:   Validates sorting instruction sequences")
	fmt.Println()
	fmt.Println("Build the programs:")
	fmt.Println("  make all")
	fmt.Println()
	fmt.Println("Usage examples:")
	fmt.Println("  ./push-swap \"4 67 3 87 23\"")
	fmt.Println("  ./push-swap \"2 1 3\" | ./checker \"2 1 3\"")
	fmt.Println()
	fmt.Println("Run tests:")
	fmt.Println("  make test")
	fmt.Println()
	fmt.Println("For more information, see README.md")
	
	os.Exit(0)
}
