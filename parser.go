package main

import (
	"fmt"
	"strconv"
	"strings"
)

// parseArguments parses command-line arguments into a slice of integers.
// It supports multiple input formats:
// - Multiple arguments: ["1", "2", "3"]
// - Single quoted string: ["1 2 3"]
// - Mixed: ["1", "2 3", "4"]
// Returns an error if any argument is not a valid integer or if duplicates are found.
func parseArguments(args []string) ([]int, error) {
	if len(args) == 0 {
		return []int{}, nil
	}

	var numbers []int
	seen := make(map[int]bool)

	for _, arg := range args {
		// Split by spaces to handle quoted arguments like "2 1 3"
		parts := strings.Fields(arg)
		for _, part := range parts {
			num, err := strconv.Atoi(part)
			if err != nil {
				return nil, fmt.Errorf("invalid integer: %s", part)
			}
			
			if seen[num] {
				return nil, fmt.Errorf("duplicate number: %d", num)
			}
			seen[num] = true
			numbers = append(numbers, num)
		}
	}

	return numbers, nil
}

// createStackFromNumbers creates a stack from a slice of integers.
// The first element in the slice becomes the top of the stack.
// Numbers are added in reverse order because stack is LIFO.
func createStackFromNumbers(numbers []int) *Stack {
	stack := NewStack()
	// Numbers are added in reverse order because stack is LIFO
	// First argument should be at the top
	for i := len(numbers) - 1; i >= 0; i-- {
		stack.Push(numbers[i])
	}
	return stack
}

// isAlreadySorted checks if a slice of integers is sorted in ascending order.
// Returns true if the slice is empty, has one element, or is sorted.
func isAlreadySorted(numbers []int) bool {
	for i := 1; i < len(numbers); i++ {
		if numbers[i-1] > numbers[i] {
			return false
		}
	}
	return true
}
