package pushswap

import "fmt"

// Operation represents a push-swap operation with its name and execution function.
type Operation struct {
	name string
	exec func(*Stack, *Stack)
}

// operations maps operation names to their corresponding Operation structs.
// This provides a lookup table for executing operations by name.
var operations = map[string]Operation{
	"pa": {"pa", pa},
	"pb": {"pb", pb},
	"sa": {"sa", sa},
	"sb": {"sb", sb},
	"ss": {"ss", ss},
	"ra": {"ra", ra},
	"rb": {"rb", rb},
	"rr": {"rr", rr},
	"rra": {"rra", rra},
	"rrb": {"rrb", rrb},
	"rrr": {"rrr", rrr},
}

// pa pushes the top element of stack b to stack a.
// Does nothing if stack b is empty.
func pa(a, b *Stack) {
	if value, ok := b.Pop(); ok {
		a.Push(value)
	}
}

// pb pushes the top element of stack a to stack b.
// Does nothing if stack a is empty.
func pb(a, b *Stack) {
	if value, ok := a.Pop(); ok {
		b.Push(value)
	}
}

// sa swaps the first 2 elements at the top of stack a.
// Does nothing if there are fewer than 2 elements.
func sa(a, b *Stack) {
	if len(a.values) >= 2 {
		a.values[len(a.values)-1], a.values[len(a.values)-2] = a.values[len(a.values)-2], a.values[len(a.values)-1]
	}
}

// sb swaps the first 2 elements at the top of stack b.
// Does nothing if there are fewer than 2 elements.
func sb(a, b *Stack) {
	if len(b.values) >= 2 {
		b.values[len(b.values)-1], b.values[len(b.values)-2] = b.values[len(b.values)-2], b.values[len(b.values)-1]
	}
}

// ss executes sa and sb simultaneously.
func ss(a, b *Stack) {
	sa(a, b)
	sb(a, b)
}

// ra rotates stack a up. The first element becomes the last one.
// All elements shift up by one position.
func ra(a, b *Stack) {
	if len(a.values) >= 2 {
		first := a.values[len(a.values)-1]
		for i := len(a.values) - 1; i > 0; i-- {
			a.values[i] = a.values[i-1]
		}
		a.values[0] = first
	}
}

// rb rotates stack b up. The first element becomes the last one.
// All elements shift up by one position.
func rb(a, b *Stack) {
	if len(b.values) >= 2 {
		first := b.values[len(b.values)-1]
		for i := len(b.values) - 1; i > 0; i-- {
			b.values[i] = b.values[i-1]
		}
		b.values[0] = first
	}
}

// rr executes ra and rb simultaneously.
func rr(a, b *Stack) {
	ra(a, b)
	rb(a, b)
}

// rra reverse rotates stack a down. The last element becomes the first one.
// All elements shift down by one position.
func rra(a, b *Stack) {
	if len(a.values) >= 2 {
		last := a.values[0]
		for i := 0; i < len(a.values)-1; i++ {
			a.values[i] = a.values[i+1]
		}
		a.values[len(a.values)-1] = last
	}
}

// rrb reverse rotates stack b down. The last element becomes the first one.
// All elements shift down by one position.
func rrb(a, b *Stack) {
	if len(b.values) >= 2 {
		last := b.values[0]
		for i := 0; i < len(b.values)-1; i++ {
			b.values[i] = b.values[i+1]
		}
		b.values[len(b.values)-1] = last
	}
}

// rrr executes rra and rrb simultaneously.
func rrr(a, b *Stack) {
	rra(a, b)
	rrb(a, b)
}

// ExecuteOperation executes a push-swap operation by name.
// Returns an error if the operation name is not recognized.
func ExecuteOperation(opName string, a, b *Stack) error {
	if op, exists := operations[opName]; exists {
		op.exec(a, b)
		return nil
	}
	return fmt.Errorf("unknown operation: %s", opName)
}
