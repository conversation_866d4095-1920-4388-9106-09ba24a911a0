package main

// Stack represents a LIFO (Last In, First Out) data structure for integers.
// It provides all the necessary operations for the push-swap algorithm.
type Stack struct {
	values []int
}

// NewStack creates and returns a new empty stack.
func NewStack() *Stack {
	return &Stack{values: make([]int, 0)}
}

// Push adds a value to the top of the stack.
func (s *Stack) Push(value int) {
	s.values = append(s.values, value)
}

// Pop removes and returns the top value from the stack.
// Returns (0, false) if the stack is empty.
func (s *Stack) Pop() (int, bool) {
	if len(s.values) == 0 {
		return 0, false
	}
	value := s.values[len(s.values)-1]
	s.values = s.values[:len(s.values)-1]
	return value, true
}

// Peek returns the top value without removing it from the stack.
// Returns (0, false) if the stack is empty.
func (s *Stack) Peek() (int, bool) {
	if len(s.values) == 0 {
		return 0, false
	}
	return s.values[len(s.values)-1], true
}

// Size returns the number of elements in the stack.
func (s *Stack) Size() int {
	return len(s.values)
}

// IsEmpty returns true if the stack contains no elements.
func (s *Stack) IsEmpty() bool {
	return len(s.values) == 0
}

// IsSorted returns true if the stack is sorted in ascending order (smallest at top).
// An empty stack or single-element stack is considered sorted.
func (s *Stack) IsSorted() bool {
	if len(s.values) <= 1 {
		return true
	}
	// Stack is sorted if elements from top to bottom are in ascending order
	// Top of stack is at the end of the slice
	for i := len(s.values) - 1; i > 0; i-- {
		if s.values[i] > s.values[i-1] {
			return false
		}
	}
	return true
}

// Clone creates and returns a deep copy of the stack.
func (s *Stack) Clone() *Stack {
	clone := NewStack()
	clone.values = make([]int, len(s.values))
	copy(clone.values, s.values)
	return clone
}

// GetValues returns a copy of the stack's values as a slice.
// The slice is ordered from bottom to top of the stack.
func (s *Stack) GetValues() []int {
	result := make([]int, len(s.values))
	copy(result, s.values)
	return result
}

// Top returns the top value without removing it from the stack.
// This is an alias for Peek(). Returns (0, false) if the stack is empty.
func (s *Stack) Top() (int, bool) {
	if len(s.values) == 0 {
		return 0, false
	}
	return s.values[len(s.values)-1], true
}

// Second returns the second element from the top without removing it.
// Returns (0, false) if the stack has fewer than 2 elements.
func (s *Stack) Second() (int, bool) {
	if len(s.values) < 2 {
		return 0, false
	}
	return s.values[len(s.values)-2], true
}
